from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
import logging
from app.core.db import SessionLocal, get_db
from app.models.job import Job, JobStatus
from app.schemas.job import JobCreate, JobResponse, SearchRequest, SearchResponse
from app.services.auth import get_current_user 
from pydantic import BaseModel
from typing import Optional

from app.services.readability import analyze_website_unified

router = APIRouter()

@router.post("/search", response_model=SearchResponse)
async def create_search_job(
    request: SearchRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    user_id = current_user.id
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid user")

    # Create new job
    new_job = Job(
        user_id=user_id,
        search_param=request.search_param,
        status=JobStatus.pending
    )
    db.add(new_job)
    db.commit()
    db.refresh(new_job)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Job {new_job.job_id} created for user {new_job.user_id} with URL {new_job.search_param}")

    # Start analysis in background (for now, we'll run it synchronously)
    # In production, you'd want to use a task queue like Celery
    try:
        logger.info(f"Job {new_job.job_id}: starting analysis")
        await analyze_website_unified(new_job.search_param, new_job.job_id)
        new_job.status = JobStatus.completed
        logger.info(f"Job {new_job.job_id}: analysis completed successfully")
    except Exception as e:
        new_job.status = JobStatus.failed
        logger.error(f"Job {new_job.job_id}: analysis failed - {str(e)}")

    db.commit()
    db.refresh(new_job)

    logger.info(f"Job {new_job.job_id}: final status {new_job.status}")

    return SearchResponse(
        job_id=new_job.job_id,
        status=new_job.status,
        search_param=new_job.search_param
    )
