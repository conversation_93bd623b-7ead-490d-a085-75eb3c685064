import asyncio
from datetime import datetime
import logging
import requests
from bs4 import BeautifulSoup
import json
import re
from collections import Counter

logger = logging.getLogger(__name__)

async def analyze_website_unified(url: str, job_id: str) -> dict:
    """Analyze website using HTTP-only approach"""
    return analyze_website_fallback(url, job_id)

def analyze_website_fallback(url: str, job_id: str) -> dict:
    """Comprehensive HTTP-only website analysis"""

    try:
        # HTTP request to get page content
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # Basic SEO analysis
        title = soup.find('title')
        title_text = title.get_text().strip() if title else ''

        meta_desc = soup.find('meta', attrs={'name': 'description'})
        meta_desc_text = meta_desc.get('content', '') if meta_desc else ''

        h1_tags = soup.find_all('h1')
        h1_count = len(h1_tags)

        h2_tags = soup.find_all('h2')
        h2_count = len(h2_tags)

        images = soup.find_all('img')
        images_without_alt = len([img for img in images if not img.get('alt')])

        links = soup.find_all('a')
        internal_links = len([link for link in links if link.get('href', '').startswith('/') or url in link.get('href', '')])
        external_links = len(links) - internal_links

        # Content analysis
        content_text = soup.get_text()
        word_count = len(content_text.split())

        # Simple readability calculation
        sentences = content_text.split('.')
        avg_sentence_length = len(content_text.split()) / max(len(sentences), 1)
        readability_score = max(0, 100 - (avg_sentence_length * 2))

        # Extract keywords
        words = content_text.lower().split()
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word.strip('.,!?;:"()[]{}') for word in words if len(word) > 3 and word not in stop_words]
        top_keywords = [word for word, count in Counter(keywords).most_common(10)]

        # Structured data extraction
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        structured_data = []
        for script in json_ld_scripts:
            try:
                data = json.loads(script.string)
                structured_data.append(data)
            except:
                pass

        # Simple entity extraction
        entities = extract_simple_entities(content_text)

        # Technical SEO
        canonical = soup.find('link', attrs={'rel': 'canonical'})
        canonical_url = canonical.get('href') if canonical else None

        viewport_meta = soup.find('meta', attrs={'name': 'viewport'})
        has_viewport = bool(viewport_meta)

        lang_attr = soup.find('html')
        lang_attribute = lang_attr.get('lang', 'not-set') if lang_attr else 'not-set'

        # Meta tags analysis
        meta_tags = soup.find_all('meta')
        meta_analysis = {
            'total_meta_tags': len(meta_tags),
            'has_description': bool(meta_desc),
            'has_keywords': bool(soup.find('meta', attrs={'name': 'keywords'})),
            'has_robots': bool(soup.find('meta', attrs={'name': 'robots'})),
            'has_og_title': bool(soup.find('meta', attrs={'property': 'og:title'})),
            'has_og_description': bool(soup.find('meta', attrs={'property': 'og:description'})),
            'has_twitter_card': bool(soup.find('meta', attrs={'name': 'twitter:card'}))
        }

        return {
            'lighthouse': {
                'basic_metrics': {
                    'seo_score': calculate_seo_score(title_text, meta_desc_text, h1_count, images_without_alt, len(images)),
                    'accessibility_score': calculate_accessibility_score(images_without_alt, len(images), has_viewport),
                    'performance_hints': get_performance_hints(soup),
                    'best_practices': get_best_practices(soup)
                }
            },
            'content': {
                'main_content': content_text[:2000] + '...' if len(content_text) > 2000 else content_text,
                'full_text': content_text,
                'content_structure': {
                    'h1_count': h1_count,
                    'h2_count': h2_count,
                    'paragraph_count': len(soup.find_all('p')),
                    'list_count': len(soup.find_all(['ul', 'ol']))
                },
                'entities': entities,
                'keywords': top_keywords,
                'readability_score': readability_score,
                'word_count': word_count,
                'content_topics': extract_content_topics(content_text)
            },
            'structured_data': {
                'json_ld': structured_data,
                'microdata': extract_microdata(soup),
                'open_graph': extract_open_graph(soup),
                'twitter_cards': extract_twitter_cards(soup)
            },
            'ai_readiness': {
                'content_organization': {
                    'has_clear_headings': h1_count > 0 and h2_count > 0,
                    'content_length': 'adequate' if word_count > 300 else 'short',
                    'structured_data_present': len(structured_data) > 0
                },
                'findability': {
                    'title_optimized': len(title_text) > 10 and len(title_text) < 60,
                    'meta_description_optimized': len(meta_desc_text) > 120 and len(meta_desc_text) < 160,
                    'heading_structure': h1_count == 1 and h2_count > 0
                }
            },
            'basic_analysis': {
                'title': title_text,
                'meta_description': meta_desc_text,
                'h1_count': h1_count,
                'h2_count': h2_count,
                'images_total': len(images),
                'images_without_alt': images_without_alt,
                'links_total': len(links),
                'internal_links': internal_links,
                'external_links': external_links,
                'word_count': word_count,
                'has_viewport_meta': has_viewport,
                'has_canonical': bool(canonical_url),
                'canonical_url': canonical_url,
                'lang_attribute': lang_attribute,
                'meta_analysis': meta_analysis
            },
            'url': url,
            'job_id': job_id,
            'timestamp': datetime.utcnow().isoformat(),
            'analysis_method': 'http_comprehensive'
        }

    except Exception as e:
        logger.error(f"Website analysis failed for {url}: {str(e)}")
        return {
            'error': f'Analysis failed: {str(e)}',
            'url': url,
            'job_id': job_id,
            'timestamp': datetime.utcnow().isoformat(),
            'analysis_method': 'http_comprehensive'
        }

def extract_simple_entities(text: str) -> list:
    """Extract simple entities without spaCy"""
    entities = []

    # Simple email detection
    emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
    for email in emails:
        entities.append({"text": email, "label": "EMAIL"})

    # Simple URL detection
    urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
    for url in urls:
        entities.append({"text": url, "label": "URL"})

    return entities[:10]  # Return top 10 entities

def calculate_seo_score(title: str, meta_desc: str, h1_count: int, images_without_alt: int, total_images: int) -> int:
    """Calculate basic SEO score"""
    score = 100

    if not title or len(title) < 10:
        score -= 20
    if not meta_desc or len(meta_desc) < 120:
        score -= 15
    if h1_count != 1:
        score -= 10
    if total_images > 0 and images_without_alt > 0:
        score -= (images_without_alt / total_images) * 20

    return max(0, int(score))

def calculate_accessibility_score(images_without_alt: int, total_images: int, has_viewport: bool) -> int:
    """Calculate basic accessibility score"""
    score = 100

    if total_images > 0 and images_without_alt > 0:
        score -= (images_without_alt / total_images) * 50
    if not has_viewport:
        score -= 20

    return max(0, int(score))

def get_performance_hints(soup) -> list:
    """Get basic performance hints"""
    hints = []

    images = soup.find_all('img')
    if len(images) > 20:
        hints.append("Consider optimizing images - found many images")

    scripts = soup.find_all('script')
    if len(scripts) > 10:
        hints.append("Consider minimizing JavaScript - found many script tags")

    return hints

def get_best_practices(soup) -> list:
    """Get basic best practices"""
    practices = []

    if not soup.find('meta', attrs={'name': 'viewport'}):
        practices.append("Add viewport meta tag for mobile responsiveness")

    if not soup.find('link', attrs={'rel': 'canonical'}):
        practices.append("Add canonical URL to avoid duplicate content")

    return practices

def extract_content_topics(text: str) -> list:
    """Extract basic topics from text"""
    topics = []
    text_lower = text.lower()

    topic_keywords = {
        'technology': ['software', 'computer', 'digital', 'tech', 'app', 'website', 'online'],
        'business': ['company', 'business', 'service', 'product', 'customer', 'market'],
        'education': ['learn', 'education', 'course', 'training', 'skill', 'knowledge'],
        'health': ['health', 'medical', 'doctor', 'treatment', 'wellness', 'care'],
        'finance': ['money', 'financial', 'investment', 'bank', 'payment', 'cost']
    }

    for topic, keywords in topic_keywords.items():
        if any(keyword in text_lower for keyword in keywords):
            topics.append(topic)

    return topics[:3]  # Return top 3 topics

def extract_microdata(soup) -> list:
    """Extract microdata from HTML"""
    microdata = []
    items = soup.find_all(attrs={'itemscope': True})
    for item in items:
        item_type = item.get('itemtype', '')
        if item_type:
            microdata.append({'type': item_type})
    return microdata

def extract_open_graph(soup) -> dict:
    """Extract Open Graph data"""
    og_data = {}
    og_tags = soup.find_all('meta', attrs={'property': lambda x: x and x.startswith('og:')})
    for tag in og_tags:
        property_name = tag.get('property', '').replace('og:', '')
        content = tag.get('content', '')
        if property_name and content:
            og_data[property_name] = content
    return og_data

def extract_twitter_cards(soup) -> dict:
    """Extract Twitter Card data"""
    twitter_data = {}
    twitter_tags = soup.find_all('meta', attrs={'name': lambda x: x and x.startswith('twitter:')})
    for tag in twitter_tags:
        name = tag.get('name', '').replace('twitter:', '')
        content = tag.get('content', '')
        if name and content:
            twitter_data[name] = content
    return twitter_data



